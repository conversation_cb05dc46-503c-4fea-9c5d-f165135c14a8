import utils
from telegram_utils import TelegramUtil
from datetime import datetime, timedelta, time
import psycopg2
import sys
import traceback
import prettytable as pt
import re
import threading
import json
from rabbitmq_channel_singleton import RabbitMQChannelSingleton
import signal
import sys
import asyncio

signal.signal(signal.SIGINT, lambda x, y: sys.exit(0))

def is_outside_business_hours():
    now = datetime.now()
    if now.weekday() in [5, 6]:
        return True  # It's a weekend
    start_time = time(hour=9, minute=15)
    end_time = time(hour=15, minute=30)
    if start_time <= now.time() <= end_time:
        return False
    return True

class UnusualOptionActivity:
    def __init__(self, tf=5, price_multiplier_range=[1,2], vol_multiplier=1, oi_multiplier=1, traded_value=0, non_index=False, send_to_mq=False) -> None:
        config = utils.get_config()
        self.telegram_bot = TelegramUtil(config=config, client=False, bot=True)
        self.telegram_chat_id = -1001862665110
        # self.telegram_chat_id2 = -819424038
        self.db_conn = psycopg2.connect(
            host=config.db_host,
            database=config.db_name,
            user=config.db_user,
            port=config.db_port
        )
        # self.mq_channel = RabbitMQChannelSingleton()
        self.send_to_mq = send_to_mq
        self.tf = tf
        self.price_multiplier_range = price_multiplier_range
        self.vol_multiplier = vol_multiplier
        self.oi_multiplier = oi_multiplier
        self.traded_value = traded_value
        self.non_index = non_index

    def to_pretty_table(self, title, msg_rows):
        table = pt.PrettyTable(['Time', 'Symbol', 'Price', 'SpotPrice'])
        table.align['Time'] = 'l'
        table.align['Symbol'] = 'l'
        table.align['Price'] = 'r'
        table.align['SpotPrice'] = 'r'
        table.add_rows(msg_rows)
        msg = "<b>{}</b><pre>{}</pre>".format(title, str(table))
        return msg

    def alert_summary_to_pretty_table(self, title, msg_rows):
        table = pt.PrettyTable(['Symbol', 'AlertedAt'])
        table.align['Symbol'] = 'l'
        table.align['AlertedAt'] = 'l'
        table.add_rows(msg_rows)
        msg = "<b>{}</b><pre>{}</pre>".format(title, str(table))
        return msg

    def send_summary_alert(self, rows):
        title = "REPEAT ALERT"
        msg_rows = []
        for row in rows:
            msg_row = [row['spot_symbol'],  row['alerted_at'].strftime("%Y-%m-%d %H:%M:%S")]
            msg_rows.append(msg_row)
        msg = self.alert_summary_to_pretty_table(title, msg_rows)
        try:
            print(str(datetime.now()), ": sending alert to telegram from thread_id: ", threading.get_ident())
            self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg)
            # if self.traded_value:
            #     self.telegram_bot.send_message(chatid=self.telegram_chat_id2, msg=msg)
        except Exception:
            traceback.print_exc()


    def send_unusual_activity_alert(self, rows):
        # rows = [dict(row) for row in query_results]
        title = f"{self.tf} mins alert:"
        if self.price_multiplier_range!=[1,2]:
            title = title + f" price_incr> {int(100*(self.price_multiplier_range[0]-1))}% |"
        if self.traded_value:
            title = title + f" traded_value > {self.traded_value} |"
        if self.vol_multiplier!=1:
            title = title + f" vol_incr > {self.vol_multiplier}x |"
        if self.oi_multiplier!=1:
            title = title + f" oi_incr > {self.oi_multiplier}x |"
        # title = f"{self.tf} mins alert:" # TODO: remove later
        msg_rows = []
        for row in rows:
            symbol = row['symbol']
            # time_from = row['time_from'].strftime("%H:%M")
            # time_to = row['time_to'].strftime("%H:%M")
            time_to = row['time_to'].strftime("%Y-%m-%d %H:%M:%S")
            # vol_from = row['vol_from']
            # vol_to = row['vol_to']
            # price_from = row['price_from']
            price_to = row['price_to']
            spot_price = row['spot_price']
            msg_row = [time_to, symbol, price_to, spot_price]
            msg_rows.append(msg_row)
            msg_rows.sort(key=lambda x: x[1])  # order by symbol

        msg = self.to_pretty_table(title, msg_rows)
        # msg = f"Buy trigerred for {symbol} in {tf} min timeframe between {time_from} and {time_to}, vol: {vol_from}->{vol_to}, price: {price_from}->{price_to}"
        try:
            print(str(datetime.now()), ": sending alert to telegram from thread_id: ", threading.get_ident())
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.telegram_bot.send_message(chatid=self.telegram_chat_id, msg=msg))
            # if self.traded_value:
            #     self.telegram_bot.send_message(chatid=self.telegram_chat_id2, msg=msg)
        except Exception:
            traceback.print_exc()

    # def send_count_alert(self, rows):
    #     market_start_time = datetime.now().replace(hour=9, minute=15, second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")

    #     repeated_symbol_query = f"""
    #         SELECT spot_symbol FROM unusual_option_activity WHERE alerted_at>%s and spot_symbol in %s GROUP BY spot_symbol HAVING count(*) > 1
    #     """

    #     repeated_alert_query = f"""
    #         SELECT spot_symbol, strike_symbol, traded_value, price_multiplier, vol_multiplier, oi_multiplier, alerted_at from unusual_option_activity where spot_symbol in %s
    #     """
    #     with self.db_conn.cursor() as cur:
    #         unique_spot_symbols = list(set([row['spot_symbol'] for row in rows]))
    #         cur.execute(repeated_symbol_query, (market_start_time, tuple(unique_spot_symbols)))
    #         symbols_with_repeated_alerts = [c[0] for c in cur.fetchall()]
    #         if symbols_with_repeated_alerts:
    #             cur.execute(repeated_alert_query, (tuple(symbols_with_repeated_alerts),))
    #             columns = [column[0] for column in cur.description]
    #             repeated_alert_rows = []
    #             for row in cur.fetchall():
    #                 repeated_alert_rows.append(dict(zip(columns, row)))
    #             if repeated_alert_rows:
    #                 self.send_summary_alert(repeated_alert_rows)

    def save_alert(self, rows):
        now = datetime.now()
        current_time_bucket = now.replace(minute=now.minute-now.minute%self.tf, second=0, microsecond=0)

        save_alert_query = f"""
            INSERT INTO unusual_option_activity (tf_min, spot_symbol, strike_symbol, traded_value, vol_multiplier, oi_multiplier, price, spot_price, alerted_at) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        with self.db_conn.cursor() as cur:
            data = [(self.tf, row['spot_symbol'], row['symbol'], self.traded_value, self.vol_multiplier if self.vol_multiplier != 1 else None, self.oi_multiplier if self.oi_multiplier != 1 else None, row['price_to'], row['spot_price'], row['time_to']) for row in rows]
            cur.executemany(save_alert_query, data)
            self.db_conn.commit()

    def save_and_send_alert(self, rows):
        self.save_alert(rows)
        if self.send_to_mq:
            q = RabbitMQChannelSingleton()
            print(datetime.now(), "sending_trades: ", [r['symbol'] for r in rows])
            q.mq_channel.basic_publish(exchange='', routing_key='quotes', body=json.dumps(rows, default=str))
        self.send_unusual_activity_alert(rows)
        # self.send_count_alert(rows)
    
    def construct_query(self):
        now = datetime.now()
        # tf = 5
        current_time_bucket = now.replace(minute=now.minute-now.minute%self.tf, second=0, microsecond=0)
        prev_time_bucket = current_time_bucket - timedelta(minutes=self.tf)
        # print("prev_time_bucket: ", prev_time_bucket)
        prev_time_bucket = prev_time_bucket.strftime("%Y-%m-%d %H:%M:%S")
        market_start_time = now.replace(hour=9, minute=15, second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
        query = f"""
            SELECT t1.symbol as symbol, t1.time as time_from, t2.time as time_to, t1.price as price_from, t2.price as price_to, t1.volume as vol_from, t2.volume as vol_to, t2.vwap as vwap, t2.ema9 as ema9, t2.ema20 as ema20
            FROM ohlc_{self.tf}min t1
            JOIN ohlc_{self.tf}min t2
            ON t1.symbol = t2.symbol AND t1.time = t2.time - INTERVAL '{self.tf} minute'
            WHERE
            t2.time = '{current_time_bucket}'
            AND t1.price!=0
            AND t2.oi!=0
            AND t2.price>=t1.price*{self.price_multiplier_range[0]} AND t2.price<t1.price*{self.price_multiplier_range[1]}
            AND t2.price >= (select max(price) from ohlc_{self.tf}min where symbol = t1.symbol and time<t2.time AND time>='{market_start_time}')
        """
        if self.traded_value:
            query = query + f" AND (t2.price-t1.price)*t2.volume >= {self.traded_value}"
        if self.vol_multiplier!=1:
            query = query + f" AND t2.volume >= (select avg(volume) from ohlc_{self.tf}min where symbol = t1.symbol and time<t2.time AND time>='{market_start_time}') * {self.vol_multiplier}"
        if self.oi_multiplier!=1:
            query = query + f" AND t2.oi >= t1.oi * {self.oi_multiplier}"
        if self.non_index:
            query = query + f" AND t1.symbol not like '%NIFTY%'"
        else:
            query = query + f" AND t1.symbol like '%NIFTY%'"
        query = query + f"AND t2.price>t2.vwap AND t2.price>t2.ema9 AND t2.ema9>t2.ema20 AND t2.vwap>t2.ema9"
        # query = query + " limit 1"
        # print(str(datetime.now()), ": ", query)
        return query

    def take_action(self):
        spot_price_query = f"""
            SELECT price from ohlc_{self.tf}min where symbol=%s order by time desc limit 1
        """
        # now = datetime.now().replace(hour=12, minute=27)
        with self.db_conn.cursor() as cur:
            query = self.construct_query()
            cur.execute(query)
            columns = [column[0] for column in cur.description]
            results = []
            for row in cur.fetchall():
                results.append(dict(zip(columns, row)))
            for res in results:
                symbol = res['symbol']
                spot_symbol = re.split(r'(\d+)', symbol)[0]
                strike_price = int(re.search(r'(\d+)(?=[CE|PE])', res['symbol']).group(1))
                cur.execute(spot_price_query, (spot_symbol, ))
                spot_price = cur.fetchone()
                spot_price = spot_price[0] if spot_price else None
                res['spot_symbol'] = spot_symbol
                res['spot_price'] = spot_price
                res['strike_price'] = strike_price
                res['ce_pe'] = res['symbol'][-2:]
            
            results = [res for res in results if ((res['strike_price']>=1.01*res['spot_price'] and res['ce_pe']=='CE') or (res['strike_price']<=0.99*res['spot_price'] and res['ce_pe']=='PE'))]
            # Remove non-option symbols if somehow crept in the table
            # results = [res for res in results if any(x in res['symbol'] for x in ['CE', 'PE'])]

            if results:
                self.save_and_send_alert(results)

# if __name__ == "__main__":
#     if is_outside_business_hours():
#         sys.exit()
#     if len(sys.argv) < 2:
#         raise ValueError("An argument must be provided.")
#     tf = int(sys.argv[1])
#     price_multiplier = 0
#     vol_multiplier = 0

#     if tf == 1:
#         price_multiplier = 1.1
#         vol_multiplier = 2
#     elif tf == 5:
#         price_multiplier = 1.1
#         vol_multiplier = 10
#     else:
#         print("oops")
#         sys.exit()
#     uva = UnusualVolAlert()
#     uva.check_and_send_alert(tf, price_multiplier=price_multiplier, vol_multiplier=vol_multiplier)

