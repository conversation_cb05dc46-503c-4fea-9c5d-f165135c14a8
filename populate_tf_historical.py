#!/usr/bin/env python3
"""
Script to backpopulate OHLC tables (5min, 15min, 1hour, daily) using Zerodha Kite historical API.
Gets all NSE F&O stocks from FilteredInstruments.nse_stocks and fetches OHLC data for specified timeframe.

Usage:
    python populate_daily_historical.py 5min 30        # 5min data for last 30 days
    python populate_daily_historical.py 15min 7        # 15min data for last 7 days
    python populate_daily_historical.py 1hour 14       # 1hour data for last 14 days
    python populate_daily_historical.py daily 30       # daily data for last 30 days
    python populate_daily_historical.py daily custom 2024-01-01 2024-01-31  # custom range
"""

import psycopg2
import pandas as pd
import utils
import time
import logging
from datetime import datetime, timedelta
from filtered_instruments import FilteredInstruments
from broker_singleton import BrokerSingleton

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

class OHLCHistoricalPopulator:
    def __init__(self, timeframe='daily'):
        """Initialize with database connection, broker, NSE F&O stocks, and timeframe."""
        self.timeframe = timeframe
        self.validate_timeframe()

        # Set table name and API interval based on timeframe
        self.table_name = f"ohlc_{timeframe}"
        self.api_interval = self.get_api_interval()

        self.config = utils.get_config()
        self.db_conn = psycopg2.connect(
            host=self.config.db_host,
            database=self.config.db_name,
            user=self.config.db_user,
            port=self.config.db_port
        )

        # Initialize broker
        self.broker = BrokerSingleton()

        # Get NSE F&O stocks from FilteredInstruments
        logging.info("Initializing FilteredInstruments to get NSE F&O stocks...")
        self.filtered_instruments = FilteredInstruments(self.broker)
        self.nse_stocks = self.filtered_instruments.nse_stocks

        logging.info(f"Found {len(self.nse_stocks)} NSE F&O stocks for {timeframe} timeframe")

        # Rate limiting - Zerodha allows 3 requests per second
        self.rate_limit_delay = 0.4  # 400ms between requests

    def validate_timeframe(self):
        """Validate the timeframe parameter."""
        valid_timeframes = ['5min', '15min', '1hour', 'daily']
        if self.timeframe not in valid_timeframes:
            raise ValueError(f"Invalid timeframe: {self.timeframe}. Must be one of {valid_timeframes}")

    def get_api_interval(self):
        """Get the Zerodha API interval string for the timeframe."""
        interval_mapping = {
            '5min': '5minute',
            '15min': '15minute',
            '1hour': 'hour',
            'daily': 'day'
        }
        return interval_mapping[self.timeframe]

    def get_date_range(self, days_back=30):
        """Get date range for the last N days."""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        # Format for Zerodha API
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')

        return start_date_str, end_date_str

    def fetch_ohlc_data_for_stock(self, stock_row):
        """Fetch OHLC data for a single stock in the specified timeframe."""
        symbol = stock_row['tradingsymbol']
        instrument_token = stock_row['instrument_token']

        logging.info(f"Fetching {self.timeframe} data for {symbol} (token: {instrument_token})")

        try:
            start_date, end_date = self.get_date_range()

            # Fetch historical data from Zerodha with specified interval
            historical_data = self.broker.kite.historical_data(
                instrument_token=instrument_token,
                from_date=start_date,
                to_date=end_date,
                interval=self.api_interval
            )

            if not historical_data:
                logging.warning(f"No historical data received for {symbol}")
                return []

            # Convert to our format
            processed_data = []
            for candle in historical_data:
                processed_data.append({
                    'time': candle['date'],
                    'symbol': symbol,
                    'price': candle['close'],  # Close price
                    'volume': candle['volume'],
                    'oi': candle.get('oi', 0)  # OI might not be available for all stocks
                })

            logging.info(f"Fetched {len(processed_data)} {self.timeframe} records for {symbol}")
            return processed_data

        except Exception as e:
            logging.error(f"Error fetching {self.timeframe} data for {symbol}: {e}")
            return []

    def insert_ohlc_data(self, ohlc_data):
        """Insert OHLC data into the specified timeframe table."""
        if not ohlc_data:
            return

        insert_query = f"""
            INSERT INTO {self.table_name} (time, symbol, price, volume, oi)
            VALUES (%s, %s, %s, %s, %s)
            ON CONFLICT (symbol, time) DO UPDATE SET
                price = EXCLUDED.price,
                volume = EXCLUDED.volume,
                oi = EXCLUDED.oi
        """

        try:
            with self.db_conn.cursor() as cur:
                data_tuples = [
                    (record['time'], record['symbol'], record['price'],
                     record['volume'], record['oi'])
                    for record in ohlc_data
                ]

                cur.executemany(insert_query, data_tuples)
                self.db_conn.commit()

            logging.info(f"Inserted {len(data_tuples)} {self.timeframe} records into {self.table_name}")

        except Exception as e:
            logging.error(f"Error inserting {self.timeframe} data: {e}")
            self.db_conn.rollback()

    def get_existing_symbols(self):
        """Get symbols that already have data in the timeframe table."""
        try:
            with self.db_conn.cursor() as cur:
                cur.execute(f"SELECT DISTINCT symbol FROM {self.table_name}")
                existing_symbols = {row[0] for row in cur.fetchall()}
            return existing_symbols
        except Exception as e:
            logging.warning(f"Could not fetch existing symbols from {self.table_name}: {e}")
            return set()

    def populate_all_stocks(self, days_back=30, skip_existing=True):
        """Populate OHLC data for all NSE F&O stocks in the specified timeframe."""
        start_date, end_date = self.get_date_range(days_back)
        logging.info(f"Populating {self.timeframe} OHLC data from {start_date} to {end_date}")

        # Filter stocks if skip_existing is True
        stocks_to_process = self.nse_stocks.copy()
        if skip_existing:
            existing_symbols = self.get_existing_symbols()
            if existing_symbols:
                stocks_to_process = stocks_to_process[
                    ~stocks_to_process['tradingsymbol'].isin(existing_symbols)
                ]
                logging.info(f"Skipping {len(existing_symbols)} stocks with existing {self.timeframe} data")

        logging.info(f"Processing {len(stocks_to_process)} stocks for {self.timeframe} timeframe...")

        successful_stocks = 0
        failed_stocks = 0
        total_records = 0
        failed_symbols = []

        for index, stock_row in stocks_to_process.iterrows():
            symbol = stock_row['tradingsymbol']

            try:
                logging.info(f"Processing stock {successful_stocks + failed_stocks + 1}/{len(stocks_to_process)}: {symbol}")

                # Fetch OHLC data for this stock
                ohlc_data = self.fetch_ohlc_data_for_stock(stock_row)

                if ohlc_data:
                    # Insert into database
                    self.insert_ohlc_data(ohlc_data)
                    successful_stocks += 1
                    total_records += len(ohlc_data)
                    logging.info(f"✅ Successfully processed {symbol}")
                else:
                    logging.warning(f"⚠️  No {self.timeframe} data fetched for {symbol}")
                    failed_stocks += 1
                    failed_symbols.append(symbol)

                # Rate limiting - wait between requests
                time.sleep(self.rate_limit_delay)

                # Progress update every 10 stocks
                if (successful_stocks + failed_stocks) % 10 == 0:
                    logging.info(f"Progress: {successful_stocks + failed_stocks}/{len(stocks_to_process)} stocks processed")

            except Exception as e:
                logging.error(f"❌ Error processing {symbol}: {e}")
                failed_stocks += 1
                failed_symbols.append(symbol)
                continue

        # Summary
        logging.info(f"\n{'='*50}")
        logging.info(f"{self.timeframe.upper()} POPULATION SUMMARY")
        logging.info('='*50)
        logging.info(f"Timeframe: {self.timeframe}")
        logging.info(f"Table: {self.table_name}")
        logging.info(f"Total stocks processed: {len(stocks_to_process)}")
        logging.info(f"Successful: {successful_stocks}")
        logging.info(f"Failed: {failed_stocks}")
        logging.info(f"Total records inserted: {total_records}")
        logging.info(f"Date range: {start_date} to {end_date}")

        if failed_symbols:
            logging.info(f"Failed symbols: {failed_symbols[:10]}{'...' if len(failed_symbols) > 10 else ''}")

        return successful_stocks, failed_stocks, total_records

    def verify_data(self):
        """Verify the populated data."""
        logging.info(f"Verifying populated {self.timeframe} data in {self.table_name}...")

        try:
            with self.db_conn.cursor() as cur:
                # Check total records
                cur.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                total_count = cur.fetchone()[0]

                # Check unique symbols
                cur.execute(f"SELECT COUNT(DISTINCT symbol) FROM {self.table_name}")
                symbol_count = cur.fetchone()[0]

                # Check date range
                cur.execute(f"SELECT MIN(time), MAX(time) FROM {self.table_name}")
                min_date, max_date = cur.fetchone()

                # Check recent data
                cur.execute(f"""
                    SELECT symbol, time, price, volume
                    FROM {self.table_name}
                    ORDER BY time DESC, symbol
                    LIMIT 5
                """)
                recent_data = cur.fetchall()

                logging.info(f"Verification Results for {self.timeframe}:")
                logging.info(f"  Table: {self.table_name}")
                logging.info(f"  Total records: {total_count}")
                logging.info(f"  Unique symbols: {symbol_count}")
                logging.info(f"  Date range: {min_date} to {max_date}")
                logging.info(f"  Recent data sample:")
                for row in recent_data:
                    logging.info(f"    {row}")

        except Exception as e:
            logging.error(f"Error during {self.timeframe} verification: {e}")

    def close_connection(self):
        """Close database connection."""
        if self.db_conn:
            self.db_conn.close()

def populate_specific_date_range(timeframe, start_date_str, end_date_str, skip_existing=True):
    """Populate OHLC data for a specific timeframe and date range."""
    logging.info(f"Populating {timeframe} OHLC data from {start_date_str} to {end_date_str}")

    try:
        populator = OHLCHistoricalPopulator(timeframe)

        # Override the get_date_range method for custom range
        original_get_date_range = populator.get_date_range
        populator.get_date_range = lambda days_back=None: (start_date_str, end_date_str)

        # Populate data
        successful, failed, total_records = populator.populate_all_stocks(skip_existing=skip_existing)

        # Restore original method
        populator.get_date_range = original_get_date_range

        # Verify the data
        populator.verify_data()

        logging.info(f"Custom {timeframe} date range population completed successfully!")
        return successful, failed, total_records

    except Exception as e:
        logging.error(f"Error in custom {timeframe} date range population: {e}")
        return 0, 0, 0

    finally:
        if 'populator' in locals():
            populator.close_connection()

def show_usage():
    """Show usage instructions."""
    print("""
Usage: python populate_daily_historical.py <timeframe> <days_back> [options]
       python populate_daily_historical.py <timeframe> custom <start_date> <end_date> [force]
       python populate_daily_historical.py test <timeframe> [days_back]

Timeframes: 5min, 15min, 1hour, daily

Examples:
  python populate_daily_historical.py daily 30                    # Daily data for last 30 days
  python populate_daily_historical.py 5min 7                     # 5min data for last 7 days
  python populate_daily_historical.py 15min 14                   # 15min data for last 14 days
  python populate_daily_historical.py 1hour 30                   # 1hour data for last 30 days
  python populate_daily_historical.py daily custom 2024-01-01 2024-01-31  # Custom range
  python populate_daily_historical.py 5min custom 2024-01-15 2024-01-16 force  # Force overwrite
  python populate_daily_historical.py test daily 7               # Test mode with daily data
    """)

def main():
    """Main function to populate OHLC data based on command line arguments."""
    import sys

    if len(sys.argv) < 2:
        show_usage()
        return

    # Parse arguments
    if sys.argv[1] == "test":
        # Test mode: python populate_daily_historical.py test <timeframe> [days_back]
        if len(sys.argv) < 3:
            logging.error("Timeframe required for test mode")
            show_usage()
            return

        timeframe = sys.argv[2]
        days_back = int(sys.argv[3]) if len(sys.argv) > 3 else 7

        logging.info(f"Running test mode: {timeframe} for last {days_back} days with 5 stocks...")

        try:
            populator = OHLCHistoricalPopulator(timeframe)

            # Limit to first 5 stocks for testing
            populator.nse_stocks = populator.nse_stocks.head(5)
            logging.info(f"Test mode: Processing only {len(populator.nse_stocks)} stocks")

            # Populate data
            successful, failed, total_records = populator.populate_all_stocks(days_back=days_back, skip_existing=False)

            # Verify the data
            populator.verify_data()

            logging.info(f"Test {timeframe} population completed successfully!")

        except Exception as e:
            logging.error(f"Error in test execution: {e}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")

        finally:
            if 'populator' in locals():
                populator.close_connection()

        return

    # Regular mode
    timeframe = sys.argv[1]

    # Validate timeframe
    valid_timeframes = ['5min', '15min', '1hour', 'daily']
    if timeframe not in valid_timeframes:
        logging.error(f"Invalid timeframe: {timeframe}. Must be one of {valid_timeframes}")
        show_usage()
        return

    if len(sys.argv) < 3:
        logging.error("Days back or 'custom' required")
        show_usage()
        return

    if sys.argv[2] == "custom":
        # Custom date range mode
        if len(sys.argv) < 5:
            logging.error("Start and end dates required for custom mode")
            show_usage()
            return

        start_date = sys.argv[3]
        end_date = sys.argv[4]
        skip_existing = len(sys.argv) < 6 or sys.argv[5].lower() != "force"

        populate_specific_date_range(timeframe, start_date, end_date, skip_existing)

    else:
        # Days back mode
        try:
            days_back = int(sys.argv[2])
        except ValueError:
            logging.error("Days back must be a number")
            show_usage()
            return

        logging.info(f"Starting {timeframe} OHLC population from Zerodha historical API...")
        logging.info(f"Timeframe: {timeframe}, Days back: {days_back}")

        try:
            populator = OHLCHistoricalPopulator(timeframe)

            # Populate data
            successful, failed, total_records = populator.populate_all_stocks(days_back=days_back)

            # Verify the data
            populator.verify_data()

            logging.info(f"{timeframe} OHLC population completed successfully!")

        except Exception as e:
            logging.error(f"Error in main execution: {e}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")

        finally:
            if 'populator' in locals():
                populator.close_connection()

if __name__ == "__main__":
    main()
