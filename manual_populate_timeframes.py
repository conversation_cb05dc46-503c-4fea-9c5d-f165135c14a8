#!/usr/bin/env python3
"""
Manual script to populate specific timeframes for testing or backfilling.
Usage: python manual_populate_timeframes.py [timeframe] [date_time]
"""

import sys
import psycopg2
import utils
import logging
from datetime import datetime
from cron_ohlc_timeframes import OHLCTimeframePopulator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)

def populate_specific_timeframe(timeframe, target_time=None):
    """Populate a specific timeframe manually."""
    if target_time is None:
        target_time = datetime.now()
    
    logging.info(f"Manually populating {timeframe} for time {target_time}")
    
    try:
        populator = OHLCTimeframePopulator()
        populator.populate_timeframe_for_symbols(timeframe, target_time)
        populator.db_conn.close()
        
        logging.info(f"Successfully populated {timeframe}")
        return True
        
    except Exception as e:
        logging.error(f"Error populating {timeframe}: {e}")
        import traceback
        logging.error(f"Traceback: {traceback.format_exc()}")
        return False

def populate_all_timeframes(target_time=None):
    """Populate all timeframes for a specific time."""
    if target_time is None:
        target_time = datetime.now()
    
    logging.info(f"Populating all timeframes for time {target_time}")
    
    timeframes = ['5min', '15min', '1hour', 'daily']
    results = {}
    
    for timeframe in timeframes:
        logging.info(f"\n--- Populating {timeframe} ---")
        results[timeframe] = populate_specific_timeframe(timeframe, target_time)
    
    # Summary
    logging.info("\n--- Results Summary ---")
    for timeframe, success in results.items():
        status = "✅ SUCCESS" if success else "❌ FAILED"
        logging.info(f"{timeframe}: {status}")
    
    return all(results.values())

def backfill_timeframes(start_time, end_time, timeframes=None):
    """Backfill timeframes for a date range."""
    if timeframes is None:
        timeframes = ['5min', '15min', '1hour', 'daily']
    
    logging.info(f"Backfilling timeframes {timeframes} from {start_time} to {end_time}")
    
    try:
        populator = OHLCTimeframePopulator()
        
        # Generate time points based on timeframes
        current = start_time
        
        while current <= end_time:
            for timeframe in timeframes:
                if populator.should_populate_timeframe(current, timeframe):
                    logging.info(f"Backfilling {timeframe} for {current}")
                    populator.populate_timeframe_for_symbols(timeframe, current)
            
            # Move to next 5-minute interval
            current = current.replace(minute=current.minute + 5)
            if current.minute >= 60:
                current = current.replace(hour=current.hour + 1, minute=0)
        
        populator.db_conn.close()
        logging.info("Backfill completed successfully")
        return True
        
    except Exception as e:
        logging.error(f"Error during backfill: {e}")
        return False

def show_usage():
    """Show usage instructions."""
    print("""
Usage: python manual_populate_timeframes.py [command] [options]

Commands:
  single <timeframe> [datetime]     - Populate single timeframe
  all [datetime]                    - Populate all timeframes
  backfill <start> <end>           - Backfill date range
  test                             - Run test population

Timeframes: 5min, 15min, 1hour, daily

Examples:
  python manual_populate_timeframes.py single 5min
  python manual_populate_timeframes.py single 5min "2024-01-15 10:30:00"
  python manual_populate_timeframes.py all
  python manual_populate_timeframes.py all "2024-01-15 15:30:00"
  python manual_populate_timeframes.py backfill "2024-01-15 09:15:00" "2024-01-15 15:30:00"
  python manual_populate_timeframes.py test
    """)

def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    command = sys.argv[1].lower()
    
    if command == "single":
        if len(sys.argv) < 3:
            logging.error("Timeframe required for single command")
            show_usage()
            return
        
        timeframe = sys.argv[2]
        if timeframe not in ['5min', '15min', '1hour', 'daily']:
            logging.error(f"Invalid timeframe: {timeframe}")
            return
        
        target_time = None
        if len(sys.argv) > 3:
            try:
                target_time = datetime.strptime(sys.argv[3], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logging.error("Invalid datetime format. Use: YYYY-MM-DD HH:MM:SS")
                return
        
        populate_specific_timeframe(timeframe, target_time)
    
    elif command == "all":
        target_time = None
        if len(sys.argv) > 2:
            try:
                target_time = datetime.strptime(sys.argv[2], "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logging.error("Invalid datetime format. Use: YYYY-MM-DD HH:MM:SS")
                return
        
        populate_all_timeframes(target_time)
    
    elif command == "backfill":
        if len(sys.argv) < 4:
            logging.error("Start and end times required for backfill")
            show_usage()
            return
        
        try:
            start_time = datetime.strptime(sys.argv[2], "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(sys.argv[3], "%Y-%m-%d %H:%M:%S")
        except ValueError:
            logging.error("Invalid datetime format. Use: YYYY-MM-DD HH:MM:SS")
            return
        
        backfill_timeframes(start_time, end_time)
    
    elif command == "test":
        # Run a test population with current time
        logging.info("Running test population...")
        current_time = datetime.now()
        
        # Round to 5-minute boundary
        test_time = current_time.replace(
            minute=(current_time.minute // 5) * 5,
            second=0,
            microsecond=0
        )
        
        logging.info(f"Test time: {test_time}")
        populate_specific_timeframe('5min', test_time)
    
    else:
        logging.error(f"Unknown command: {command}")
        show_usage()

if __name__ == "__main__":
    main()
