-- Script to update OHLC triggers for continuous calculations across day changes
-- This script removes daily session constraints from EMA and price change calculations

-- =====================================================
-- STEP 1: Drop existing triggers from timeframe tables
-- =====================================================

-- Drop triggers from ohlc_15min
DROP TRIGGER IF EXISTS trg_ohlc_15min_before_insert_emas ON ohlc_15min;
DROP TRIGGER IF EXISTS trg_ohlc_15min_before_insert_price_change ON ohlc_15min;

-- Drop triggers from ohlc_1hour  
DROP TRIGGER IF EXISTS trg_ohlc_1hour_before_insert_emas ON ohlc_1hour;
DROP TRIGGER IF EXISTS trg_ohlc_1hour_before_insert_price_change ON ohlc_1hour;

-- Drop triggers from ohlc_daily
DROP TRIGGER IF EXISTS trg_ohlc_daily_before_insert_emas ON ohlc_daily;
DROP TRIGGER IF EXISTS trg_ohlc_daily_before_insert_price_change ON ohlc_daily;

-- =====================================================
-- STEP 2: Create new continuous calculation functions
-- =====================================================

-- Function for continuous EMA calculation (no daily reset)
CREATE OR REPLACE FUNCTION public.calculate_continuous_emas()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    prev_candle_time TIMESTAMP WITH TIME ZONE;
    prev_ema9 DOUBLE PRECISION;
    prev_ema20 DOUBLE PRECISION;
    alpha9 DOUBLE PRECISION := 2.0 / (9 + 1);   -- Multiplier for 9-period EMA
    alpha20 DOUBLE PRECISION := 2.0 / (20 + 1); -- Multiplier for 20-period EMA
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;
    
    -- Find the timestamp of the immediately preceding candle for the same symbol
    -- No daily session constraints - look for any previous candle
    EXECUTE format('
        SELECT MAX(t.time)
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
    ', table_name)
    INTO prev_candle_time
    USING NEW.symbol, NEW.time;

    -- If prev_candle_time is NULL, this is the first candle for this symbol
    IF prev_candle_time IS NULL THEN
        NEW.ema9 := NEW.price;  -- Seed EMA with the current price
        NEW.ema20 := NEW.price; -- Seed EMA with the current price
    ELSE
        -- Fetch the EMAs from that specific previous candle
        EXECUTE format('
            SELECT t.ema9, t.ema20
            FROM %I t
            WHERE t.symbol = $1
            AND t.time = $2
        ', table_name)
        INTO prev_ema9, prev_ema20
        USING NEW.symbol, prev_candle_time;

        -- Handle case where previous EMAs might be NULL
        IF prev_ema9 IS NULL THEN
            NEW.ema9 := NEW.price;
        ELSE
            NEW.ema9 := (NEW.price * alpha9) + (prev_ema9 * (1.0 - alpha9));
        END IF;

        IF prev_ema20 IS NULL THEN
            NEW.ema20 := NEW.price;
        ELSE
            NEW.ema20 := (NEW.price * alpha20) + (prev_ema20 * (1.0 - alpha20));
        END IF;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- Function for continuous price change calculation (no daily reset)
CREATE OR REPLACE FUNCTION public.calculate_continuous_price_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    prev_candle_time_found TIMESTAMP WITH TIME ZONE;
    prev_candle_price DOUBLE PRECISION;
    table_name TEXT;
BEGIN
    -- Get the table name from TG_TABLE_NAME
    table_name := TG_TABLE_NAME;
    
    -- Find the price of the immediately preceding candle for the same symbol
    -- No daily session constraints - look for any previous candle
    EXECUTE format('
        SELECT time, price
        FROM %I t
        WHERE t.symbol = $1
        AND t.time < $2
        ORDER BY t.time DESC
        LIMIT 1
    ', table_name)
    INTO prev_candle_time_found, prev_candle_price
    USING NEW.symbol, NEW.time;

    IF prev_candle_time_found IS NULL OR prev_candle_price IS NULL OR prev_candle_price = 0 THEN
        -- This is the first candle for this symbol, or previous price was 0
        NEW.price_change := 0.0;
    ELSE
        NEW.price_change := ((NEW.price - prev_candle_price) / prev_candle_price) * 100.0;
    END IF;

    RETURN NEW; -- Return the modified row to be inserted
END;
$function$;

-- =====================================================
-- STEP 3: Create new triggers for continuous calculations
-- =====================================================

-- Create triggers for ohlc_15min
CREATE TRIGGER trg_ohlc_15min_before_insert_emas
    BEFORE INSERT OR UPDATE ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_15min_before_insert_price_change
    BEFORE INSERT OR UPDATE ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Keep the existing VWAP trigger (daily calculation is appropriate for VWAP)
CREATE TRIGGER trg_ohlc_15min_before_insert_vwap
    BEFORE INSERT OR UPDATE ON ohlc_15min
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_vwap();

-- Create triggers for ohlc_1hour
CREATE TRIGGER trg_ohlc_1hour_before_insert_emas
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_1hour_before_insert_price_change
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Keep the existing VWAP trigger (daily calculation is appropriate for VWAP)
CREATE TRIGGER trg_ohlc_1hour_before_insert_vwap
    BEFORE INSERT OR UPDATE ON ohlc_1hour
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_vwap();

-- Create triggers for ohlc_daily
CREATE TRIGGER trg_ohlc_daily_before_insert_emas
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_emas();

CREATE TRIGGER trg_ohlc_daily_before_insert_price_change
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_continuous_price_change();

-- Keep the existing VWAP trigger (daily calculation is appropriate for VWAP)
CREATE TRIGGER trg_ohlc_daily_before_insert_vwap
    BEFORE INSERT OR UPDATE ON ohlc_daily
    FOR EACH ROW
    EXECUTE FUNCTION calculate_daily_vwap();

-- =====================================================
-- STEP 4: Verification queries
-- =====================================================

-- Check that triggers are created correctly
SELECT
    schemaname,
    tablename,
    triggername,
    triggerdef
FROM pg_triggers
WHERE tablename IN ('ohlc_15min', 'ohlc_1hour', 'ohlc_daily')
ORDER BY tablename, triggername;

-- =====================================================
-- USAGE INSTRUCTIONS
-- =====================================================

/*
To execute this script:

1. Connect to your PostgreSQL database
2. Run this script: \i update_triggers_continuous.sql

Key Changes Made:
- Removed daily trading session time constraints from EMA calculations
- Removed daily trading session time constraints from price change calculations
- EMAs now continue across day boundaries using the last available EMA values
- Price changes now calculate from the immediately previous candle regardless of day
- VWAP triggers remain unchanged (daily calculation is appropriate for VWAP)

Impact:
- EMA9 and EMA20 will now be truly continuous across trading days
- Price changes will show change from previous candle even across day boundaries
- This provides better technical analysis continuity for multi-day trends

Tables Affected:
- ohlc_15min
- ohlc_1hour
- ohlc_daily

Note: ohlc_1min table is not modified as it uses the original daily functions
which are still appropriate for minute-level data within trading sessions.
*/
